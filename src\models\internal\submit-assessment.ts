import { QuestionResponse } from '@tess-f/sql-tables/dist/evaluations/question-response.js'
import { z } from 'zod'
import { zodGUID } from '@tess-f/backend-utils/validators'

/**
 * Request payload for submitting a completed assessment
 */
export interface SubmitAssessmentRequest {
  /** The session ID for this assessment attempt */
  sessionId: string
  /** Array of all question responses */
  responses: QuestionResponse[]
  /** Optional overall notes for the assessment */
  notes?: string
  /** Assessment start time (ISO 8601 format) */
  startTime: string
  /** Assessment end time (ISO 8601 format) */
  endTime: string
}

/**
 * Response payload after successful assessment submission
 */
export interface SubmitAssessmentResponse {
  /** The session ID */
  sessionId: string
  /** Whether the assessment was passed */
  passed: boolean
  /** Total score achieved */
  totalScore: number
  /** Maximum possible score */
  maxScore: number
  /** Number of responses processed */
  responsesCount: number
  /** Number of questions that were graded */
  gradedQuestionsCount: number
  /** Number of questions pending manual grading */
  pendingQuestionsCount: number
}

// Validation schemas
export const questionResponseSchema = z.object({
  Id: zodGUID.optional(),
  SessionId: zodGUID.optional(),
  QuestionId: zodGUID,
  QuestionVersion: z.number().int().positive(),
  OptionId: zodGUID.optional(),
  OptionVersion: z.number().int().positive().optional(),
  TargetOptionId: zodGUID.optional(),
  TargetOptionVersion: z.number().int().positive().optional(),
  ResponseText: z.string().optional(),
  Notes: z.string().optional(),
  Correct: z.boolean().optional(),
  Score: z.number().optional(),
  Duration: z.string().regex(/^PT(?:\d+H)?(?:\d+M)?(?:\d+(?:\.\d+)?S)?$/).optional(), // ISO 8601 duration
  OrderId: z.number().int().optional()
}).superRefine((data, ctx) => {
  // Validate option ID/version pairs
  if (data.OptionId && !data.OptionVersion) {
    ctx.addIssue({
      code: 'custom',
      message: 'OptionVersion is required when OptionId is provided',
      path: ['OptionVersion']
    })
  }
  if (data.TargetOptionId && !data.TargetOptionVersion) {
    ctx.addIssue({
      code: 'custom',
      message: 'TargetOptionVersion is required when TargetOptionId is provided',
      path: ['TargetOptionVersion']
    })
  }
})

export const submitAssessmentRequestSchema = z.object({
  sessionId: zodGUID,
  responses: z.array(questionResponseSchema).min(1),
  notes: z.string().optional(),
  startTime: z.iso.datetime(),
  endTime: z.iso.datetime()
}).superRefine((data, ctx) => {
  // Validate that endTime is after startTime
  const start = new Date(data.startTime)
  const end = new Date(data.endTime)
  if (end <= start) {
    ctx.addIssue({
      code: 'custom',
      message: 'endTime must be after startTime',
      path: ['endTime']
    })
  }
})
