import { expect } from 'chai'
import Sinon from 'sinon'
import esmock from 'esmock'
import logger from '@lcs/logger'
import httpMocks from 'node-mocks-http'
import httpStatus from 'http-status'

describe('HTTP submit assessment controller', () => {
  before(() => logger.init({ level: 'error' }))
  afterEach(() => Sinon.restore())

  it('returns success when assessment is submitted successfully', async () => {
    const mockSubmissionResult = {
      sessionId: 'test-session-id',
      passed: true,
      totalScore: 85,
      maxScore: 100,
      responsesCount: 10,
      gradedQuestionsCount: 10,
      pendingQuestionsCount: 0
    }

    const submitAssessmentStub = Sinon.stub().resolves(mockSubmissionResult)

    const controller = await esmock('./submit-assessment.controller.js', {
      '../../../services/internal/submit-assessment-service.js': {
        default: submitAssessmentStub
      }
    })

    const mocks = httpMocks.createMocks({
      headers: { 'x-request-id': 'test-request-id' },
      body: {
        sessionId: 'test-session-id',
        responses: [
          {
            QuestionId: 'q1',
            QuestionVersion: 1,
            OptionId: 'opt1',
            Duration: 30000
          }
        ],
        startTime: '2023-01-01T10:00:00Z',
        endTime: '2023-01-01T10:30:00Z'
      }
    })

    await controller.default(mocks.req, mocks.res)

    expect(mocks.res.statusCode).to.equal(httpStatus.OK)
    const responseData = JSON.parse(mocks.res._getData())

    expect(responseData).to.have.property('success', true)
    expect(responseData).to.have.property('data')
    expect(responseData.data).to.have.property('sessionId', 'test-session-id')
    expect(responseData.data).to.have.property('passed', true)
    expect(responseData.data).to.have.property('totalScore', 85)
    expect(responseData).to.have.property('message', 'Assessment submitted successfully')
  })

  it('handles optional notes in request', async () => {
    const mockSubmissionResult = {
      sessionId: 'test-session-id',
      passed: false,
      totalScore: 60,
      maxScore: 100,
      responsesCount: 5,
      gradedQuestionsCount: 5,
      pendingQuestionsCount: 0
    }

    const submitAssessmentStub = Sinon.stub().resolves(mockSubmissionResult)

    const controller = await esmock('./submit-assessment.controller.js', {
      '../../../services/internal/submit-assessment-service.js': {
        default: submitAssessmentStub
      }
    })

    const mocks = httpMocks.createMocks({
      headers: { 'x-request-id': 'test-request-id' },
      body: {
        sessionId: 'test-session-id',
        responses: [],
        startTime: '2023-01-01T10:00:00Z',
        endTime: '2023-01-01T10:30:00Z',
        notes: 'Student had technical difficulties'
      }
    })

    await controller.default(mocks.req, mocks.res)

    expect(mocks.res.statusCode).to.equal(httpStatus.OK)
    const responseData = JSON.parse(mocks.res._getData())

    expect(responseData).to.have.property('success', true)
    expect(responseData.data).to.have.property('passed', false)
    expect(responseData.data).to.have.property('totalScore', 60)
  })

  it('returns 400 for validation errors', async () => {
    const submitAssessmentStub = Sinon.stub().rejects(new Error('Invalid request data: endTime is required'))

    const controller = await esmock('./submit-assessment.controller.js', {
      '../../../services/internal/submit-assessment-service.js': {
        default: submitAssessmentStub
      }
    })

    const mocks = httpMocks.createMocks({
      headers: { 'x-request-id': 'test-request-id' },
      body: {
        sessionId: 'test-session-id',
        responses: []
        // Missing required endTime
      }
    })

    await controller.default(mocks.req, mocks.res)

    expect(mocks.res.statusCode).to.equal(httpStatus.BAD_REQUEST)
    const responseData = JSON.parse(mocks.res._getData())
    expect(responseData).to.have.property('success', false)
    expect(responseData).to.have.property('error')
    expect(responseData.error).to.include('Invalid request data')
    expect(responseData).to.have.property('message', 'Failed to submit assessment')
  })

  it('returns 404 for not found errors', async () => {
    const submitAssessmentStub = Sinon.stub().rejects(new Error('Session not found'))

    const controller = await esmock('./submit-assessment.controller.js', {
      '../../../services/internal/submit-assessment-service.js': {
        default: submitAssessmentStub
      }
    })

    const mocks = httpMocks.createMocks({
      headers: { 'x-request-id': 'test-request-id' },
      body: {
        sessionId: 'non-existent-session',
        responses: [],
        startTime: '2023-01-01T10:00:00Z',
        endTime: '2023-01-01T10:30:00Z'
      }
    })

    await controller.default(mocks.req, mocks.res)

    expect(mocks.res.statusCode).to.equal(httpStatus.NOT_FOUND)
    const responseData = JSON.parse(mocks.res._getData())
    expect(responseData).to.have.property('success', false)
    expect(responseData).to.have.property('error', 'Session not found')
    expect(responseData).to.have.property('message', 'Failed to submit assessment')
  })

  it('returns 500 for general server errors', async () => {
    const submitAssessmentStub = Sinon.stub().rejects(new Error('Database connection failed'))

    const controller = await esmock('./submit-assessment.controller.js', {
      '../../../services/internal/submit-assessment-service.js': {
        default: submitAssessmentStub
      }
    })

    const mocks = httpMocks.createMocks({
      headers: { 'x-request-id': 'test-request-id' },
      body: {
        sessionId: 'test-session-id',
        responses: [],
        startTime: '2023-01-01T10:00:00Z',
        endTime: '2023-01-01T10:30:00Z'
      }
    })

    await controller.default(mocks.req, mocks.res)

    expect(mocks.res.statusCode).to.equal(httpStatus.INTERNAL_SERVER_ERROR)
    const responseData = JSON.parse(mocks.res._getData())
    expect(responseData).to.have.property('success', false)
    expect(responseData).to.have.property('error', 'Database connection failed')
    expect(responseData).to.have.property('message', 'Failed to submit assessment')
  })
})
